{"GeneratedCodeFiles": [], "GeneratedXamlFiles": ["D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\obj\\ARM64\\Debug\\net8.0-windows10.0.19041.0\\App.xaml", "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\obj\\ARM64\\Debug\\net8.0-windows10.0.19041.0\\MainWindow.xaml"], "GeneratedXbfFiles": ["D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\obj\\ARM64\\Debug\\net8.0-windows10.0.19041.0\\App.xbf", "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\obj\\ARM64\\Debug\\net8.0-windows10.0.19041.0\\MainWindow.xbf"], "GeneratedXamlPagesFiles": [], "MSBuildLogEntries": [{"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 11:42:09: 801 perfXC_StartPass2, BowelSoundLabeler"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 11:42:09: 822 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 11:42:09: 894 perfXC_FingerprintCheck, Using managed HashForWinMD"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 11:42:09: 911 perfXC_RestoredGeneratedPass2CodeFileBackup"}, {"SubCategory": null, "ErrorCode": null, "HelpKeyword": null, "File": null, "LineNumber": 0, "ColumnNumber": 0, "EndLineNumber": 0, "EndColumnNumber": 0, "Message": "Xaml Compiler Marker: 11:42:09: 917 perfXC_RestoredTypeInfoBackup"}]}