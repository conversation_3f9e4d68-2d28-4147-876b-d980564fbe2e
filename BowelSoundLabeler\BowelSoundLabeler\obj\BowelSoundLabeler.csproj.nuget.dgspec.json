{"format": 1, "restore": {"D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\BowelSoundLabeler.csproj": {}}, "projects": {"D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\BowelSoundLabeler.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\BowelSoundLabeler.csproj", "projectName": "BowelSoundLabeler", "projectPath": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\BowelSoundLabeler.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\PhD\\Code\\C#\\work\\BowelSoundLabeler\\BowelSoundLabeler\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0-windows10.0.19041.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0-windows10.0.19041": {"targetAlias": "net8.0-windows10.0.19041.0", "dependencies": {"CommunityToolkit.Mvvm": {"target": "Package", "version": "[8.2.2, )"}, "Microsoft.Extensions.Configuration": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Json": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Hosting": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Debug": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Windows.SDK.BuildTools": {"target": "Package", "version": "[10.0.26100.4948, )"}, "Microsoft.WindowsAppSDK": {"target": "Package", "version": "[1.7.250606001, )"}, "NAudio": {"target": "Package", "version": "[2.2.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "downloadDependencies": [{"name": "Microsoft.AspNetCore.App.Runtime.win-arm64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.AspNetCore.App.Runtime.win-x86", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Runtime.win-arm64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.NETCore.App.Runtime.win-x86", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.Windows.SDK.NET.Ref", "version": "[10.0.19041.57, 10.0.19041.57]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-arm64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x64", "version": "[8.0.19, 8.0.19]"}, {"name": "Microsoft.WindowsDesktop.App.Runtime.win-x86", "version": "[8.0.19, 8.0.19]"}], "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}, "Microsoft.Windows.SDK.NET.Ref.Windows": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.304/PortableRuntimeIdentifierGraph.json"}}, "runtimes": {"win-arm64": {"#import": []}, "win-x64": {"#import": []}, "win-x86": {"#import": []}}}}}