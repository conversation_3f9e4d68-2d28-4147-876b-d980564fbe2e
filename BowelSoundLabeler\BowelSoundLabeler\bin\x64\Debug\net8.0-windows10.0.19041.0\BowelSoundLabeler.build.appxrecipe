﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Machine>DESKTOP-PMTGQNT</Machine>
    <WindowsUser>jie</WindowsUser>
    <TargetPlatformIdentifier>Windows</TargetPlatformIdentifier>
    <TargetOsVersion>10.0</TargetOsVersion>
    <TargetOsDescription>Windows 10.0</TargetOsDescription>
    <SolutionConfiguration>Debug|x64</SolutionConfiguration>
    <PackageArchitecture>x64</PackageArchitecture>
    <PackageIdentityName>098f3ffc-15b4-4f7b-9d9f-bc282c5820ae</PackageIdentityName>
    <PackageIdentityPublisher>CN=jie</PackageIdentityPublisher>
    <IntermediateOutputPath>D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\obj\x64\Debug\net8.0-windows10.0.19041.0\</IntermediateOutputPath>
    <RemoteDeploymentType>CopyToDevice</RemoteDeploymentType>
    <PackageRegistrationPath></PackageRegistrationPath>
    <RemoveNonLayoutFiles>false</RemoveNonLayoutFiles>
    <DeployOptionalPackages>false</DeployOptionalPackages>
    <WindowsSdkPath>C:\Program Files %28x86%29\Windows Kits\10\</WindowsSdkPath>
    <LayoutDir>D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\AppX</LayoutDir>
  </PropertyGroup>
  <ItemGroup>
    <AppXManifest Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\AppxManifest.xml">
      <PackagePath>AppxManifest.xml</PackagePath>
      <ReRegisterAppIfChanged>true</ReRegisterAppIfChanged>
    </AppXManifest>
  </ItemGroup>
  <ItemGroup>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\resources.pri">
      <PackagePath>resources.pri</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\BowelSoundLabeler.runtimeconfig.json">
      <PackagePath>BowelSoundLabeler.runtimeconfig.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\BowelSoundLabeler.dll">
      <PackagePath>BowelSoundLabeler.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\obj\x64\Debug\net8.0-windows10.0.19041.0\apphost.exe">
      <PackagePath>BowelSoundLabeler.exe</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\Microsoft.Windows.SDK.NET.dll">
      <PackagePath>Microsoft.Windows.SDK.NET.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windows.sdk.net.ref\10.0.19041.57\lib\net8.0\WinRT.Runtime.dll">
      <PackagePath>WinRT.Runtime.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\communitytoolkit.mvvm\8.2.2\lib\net6.0\CommunityToolkit.Mvvm.dll">
      <PackagePath>CommunityToolkit.Mvvm.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Configuration.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.binder\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Binder.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Binder.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.commandline\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.CommandLine.dll">
      <PackagePath>Microsoft.Extensions.Configuration.CommandLine.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.environmentvariables\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.EnvironmentVariables.dll">
      <PackagePath>Microsoft.Extensions.Configuration.EnvironmentVariables.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.fileextensions\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.FileExtensions.dll">
      <PackagePath>Microsoft.Extensions.Configuration.FileExtensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.json\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.Json.dll">
      <PackagePath>Microsoft.Extensions.Configuration.Json.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.configuration.usersecrets\8.0.0\lib\net8.0\Microsoft.Extensions.Configuration.UserSecrets.dll">
      <PackagePath>Microsoft.Extensions.Configuration.UserSecrets.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection\8.0.0\lib\net8.0\Microsoft.Extensions.DependencyInjection.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.dependencyinjection.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.DependencyInjection.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics\8.0.0\lib\net8.0\Microsoft.Extensions.Diagnostics.dll">
      <PackagePath>Microsoft.Extensions.Diagnostics.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.diagnostics.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Diagnostics.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Diagnostics.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.fileproviders.physical\8.0.0\lib\net8.0\Microsoft.Extensions.FileProviders.Physical.dll">
      <PackagePath>Microsoft.Extensions.FileProviders.Physical.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.filesystemglobbing\8.0.0\lib\net8.0\Microsoft.Extensions.FileSystemGlobbing.dll">
      <PackagePath>Microsoft.Extensions.FileSystemGlobbing.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting\8.0.0\lib\net8.0\Microsoft.Extensions.Hosting.dll">
      <PackagePath>Microsoft.Extensions.Hosting.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.hosting.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Hosting.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Hosting.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.dll">
      <PackagePath>Microsoft.Extensions.Logging.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.abstractions\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Abstractions.dll">
      <PackagePath>Microsoft.Extensions.Logging.Abstractions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.configuration\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Configuration.dll">
      <PackagePath>Microsoft.Extensions.Logging.Configuration.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.console\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Console.dll">
      <PackagePath>Microsoft.Extensions.Logging.Console.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.debug\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.Debug.dll">
      <PackagePath>Microsoft.Extensions.Logging.Debug.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.eventlog\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.EventLog.dll">
      <PackagePath>Microsoft.Extensions.Logging.EventLog.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.logging.eventsource\8.0.0\lib\net8.0\Microsoft.Extensions.Logging.EventSource.dll">
      <PackagePath>Microsoft.Extensions.Logging.EventSource.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options\8.0.0\lib\net8.0\Microsoft.Extensions.Options.dll">
      <PackagePath>Microsoft.Extensions.Options.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.options.configurationextensions\8.0.0\lib\net8.0\Microsoft.Extensions.Options.ConfigurationExtensions.dll">
      <PackagePath>Microsoft.Extensions.Options.ConfigurationExtensions.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.extensions.primitives\8.0.0\lib\net8.0\Microsoft.Extensions.Primitives.dll">
      <PackagePath>Microsoft.Extensions.Primitives.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Graphics.Imaging.Projection.dll">
      <PackagePath>Microsoft.Graphics.Imaging.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.InteractiveExperiences.Projection.dll">
      <PackagePath>Microsoft.InteractiveExperiences.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Security.Authentication.OAuth.Projection.dll">
      <PackagePath>Microsoft.Security.Authentication.OAuth.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.WinUI.dll">
      <PackagePath>Microsoft.WinUI.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.ContentSafety.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.ContentSafety.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.Imaging.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Imaging.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AI.Text.Projection.dll">
      <PackagePath>Microsoft.Windows.AI.Text.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppLifecycle.Projection.dll">
      <PackagePath>Microsoft.Windows.AppLifecycle.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Builder.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Builder.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.AppNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.AppNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.Background.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Background.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.DynamicDependency.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.Resources.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.Resources.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll">
      <PackagePath>Microsoft.Windows.ApplicationModel.WindowsAppRuntime.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.BadgeNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.BadgeNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Management.Deployment.Projection.dll">
      <PackagePath>Microsoft.Windows.Management.Deployment.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Media.Capture.Projection.dll">
      <PackagePath>Microsoft.Windows.Media.Capture.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.PushNotifications.Projection.dll">
      <PackagePath>Microsoft.Windows.PushNotifications.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Security.AccessControl.Projection.dll">
      <PackagePath>Microsoft.Windows.Security.AccessControl.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Storage.Projection.dll">
      <PackagePath>Microsoft.Windows.Storage.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Power.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Power.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.System.Projection.dll">
      <PackagePath>Microsoft.Windows.System.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.Windows.Widgets.Projection.dll">
      <PackagePath>Microsoft.Windows.Widgets.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\lib\net6.0-windows10.0.18362.0\Microsoft.WindowsAppRuntime.Bootstrap.Net.dll">
      <PackagePath>Microsoft.WindowsAppRuntime.Bootstrap.Net.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio\2.2.1\lib\net6.0-windows7.0\NAudio.dll">
      <PackagePath>NAudio.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.asio\2.2.1\lib\netstandard2.0\NAudio.Asio.dll">
      <PackagePath>NAudio.Asio.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.core\2.2.1\lib\netstandard2.0\NAudio.Core.dll">
      <PackagePath>NAudio.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.midi\2.2.1\lib\netstandard2.0\NAudio.Midi.dll">
      <PackagePath>NAudio.Midi.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.wasapi\2.2.1\lib\netstandard2.0\NAudio.Wasapi.dll">
      <PackagePath>NAudio.Wasapi.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.winforms\2.2.1\lib\netcoreapp3.1\NAudio.WinForms.dll">
      <PackagePath>NAudio.WinForms.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\naudio.winmm\2.2.1\lib\netstandard2.0\NAudio.WinMM.dll">
      <PackagePath>NAudio.WinMM.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2903.40\runtimes\win-arm64\native\WebView2Loader.dll">
      <PackagePath>runtimes\win-arm64\native\WebView2Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2903.40\runtimes\win-x64\native\WebView2Loader.dll">
      <PackagePath>runtimes\win-x64\native\WebView2Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2903.40\runtimes\win-x86\native\WebView2Loader.dll">
      <PackagePath>runtimes\win-x86\native\WebView2Loader.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-arm64\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll">
      <PackagePath>runtimes\win-arm64\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-arm64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>runtimes\win-arm64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-arm64ec\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll">
      <PackagePath>runtimes\win-arm64ec\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-arm64ec\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>runtimes\win-arm64ec\native\Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-x64\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll">
      <PackagePath>runtimes\win-x64\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>runtimes\win-x64\native\Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-x86\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll">
      <PackagePath>runtimes\win-x86\native\Microsoft.Windows.ApplicationModel.Background.UniversalBGTask.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\runtimes\win-x86\native\Microsoft.WindowsAppRuntime.Bootstrap.dll">
      <PackagePath>runtimes\win-x86\native\Microsoft.WindowsAppRuntime.Bootstrap.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2903.40\runtimes\win-x64\native_uap\Microsoft.Web.WebView2.Core.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="C:\Users\<USER>\.nuget\packages\microsoft.web.webview2\1.0.2903.40\lib_manual\net8.0-windows10.0.17763.0\Microsoft.Web.WebView2.Core.Projection.dll">
      <PackagePath>Microsoft.Web.WebView2.Core.Projection.dll</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\bin\x64\Debug\net8.0-windows10.0.19041.0\BowelSoundLabeler.deps.json">
      <PackagePath>BowelSoundLabeler.deps.json</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\LockScreenLogo.scale-200.png">
      <PackagePath>Assets\LockScreenLogo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\SplashScreen.scale-200.png">
      <PackagePath>Assets\SplashScreen.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Square150x150Logo.scale-200.png">
      <PackagePath>Assets\Square150x150Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Square44x44Logo.scale-200.png">
      <PackagePath>Assets\Square44x44Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Square44x44Logo.targetsize-24_altform-unplated.png">
      <PackagePath>Assets\Square44x44Logo.targetsize-24_altform-unplated.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\StoreLogo.png">
      <PackagePath>Assets\StoreLogo.png</PackagePath>
    </AppxPackagedFile>
    <AppxPackagedFile Include="D:\PhD\Code\C#\work\BowelSoundLabeler\BowelSoundLabeler\Assets\Wide310x150Logo.scale-200.png">
      <PackagePath>Assets\Wide310x150Logo.scale-200.png</PackagePath>
    </AppxPackagedFile>
  </ItemGroup>
  <ItemGroup>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>x86</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>win32</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x86\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>x64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-x64\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
    <ResolvedSDKReference Include="C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.7.msix">
      <Name>Microsoft.WindowsAppRuntime.1.7</Name>
      <Version>7000.522.1444.0</Version>
      <Architecture>arm64</Architecture>
      <FrameworkIdentity>Name = Microsoft.WindowsAppRuntime.1.7, MinVersion = 7000.522.1444.0, Publisher = %27CN=Microsoft Corporation, O=Microsoft Corporation, L=Redmond, S=Washington, C=US%27</FrameworkIdentity>
      <AppxLocation>C:\Users\<USER>\.nuget\packages\microsoft.windowsappsdk\1.7.250606001\buildTransitive\..\tools\MSIX\win10-arm64\Microsoft.WindowsAppRuntime.1.7.msix</AppxLocation>
      <MoreInfo></MoreInfo>
    </ResolvedSDKReference>
  </ItemGroup>
</Project>
