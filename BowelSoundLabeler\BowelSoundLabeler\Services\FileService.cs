using BowelSoundLabeler.Models;
using Microsoft.Extensions.Logging;
using Windows.Storage;
using Windows.Storage.Pickers;
using System.Runtime.InteropServices;
using System.Text;
using System.Runtime.InteropServices.WindowsRuntime;

namespace BowelSoundLabeler.Services
{
    /// <summary>
    /// 文件服务实现 - 使用Win32 API支持初始文件夹设置
    /// </summary>
    public class FileService : IFileService
    {
        private readonly ILogger<FileService> _logger;
        private readonly IReadOnlyList<string> _supportedExtensions = new[]
        {
            ".wav", ".mp3", ".m4a", ".flac", ".mat", ".csv"
        };

        // Win32 API 声明
        [DllImport("comdlg32.dll", SetLastError = true, CharSet = CharSet.Auto)]
        private static extern bool GetOpenFileName(ref OPENFILENAME ofn);

        [DllImport("user32.dll")]
        private static extern IntPtr GetActiveWindow();

        [StructLayout(LayoutKind.Sequential, CharSet = CharSet.Auto)]
        private struct OPENFILENAME
        {
            public int lStructSize;
            public IntPtr hwndOwner;
            public IntPtr hInstance;
            public string lpstrFilter;
            public string lpstrCustomFilter;
            public int nMaxCustFilter;
            public int nFilterIndex;
            public string lpstrFile;
            public int nMaxFile;
            public string lpstrFileTitle;
            public int nMaxFileTitle;
            public string lpstrInitialDir;
            public string lpstrTitle;
            public int Flags;
            public short nFileOffset;
            public short nFileExtension;
            public string lpstrDefExt;
            public IntPtr lCustData;
            public IntPtr lpfnHook;
            public string lpTemplateName;
        }

        private const int OFN_ALLOWMULTISELECT = 0x00000200;
        private const int OFN_EXPLORER = 0x00080000;
        private const int OFN_FILEMUSTEXIST = 0x00001000;
        private const int OFN_PATHMUSTEXIST = 0x00000800;

        public FileService(ILogger<FileService> logger)
        {
            _logger = logger;
        }

        public async Task<AudioFile?> SelectSingleFileAsync(string? initialFolder = null)
        {
            // 如果提供了初始文件夹，优先使用Win32 API
            if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
            {
                try
                {
                    _logger.LogInformation("使用Win32 API选择单个文件，初始文件夹: {Folder}", initialFolder);
                    var result = await SelectSingleFileWithWin32Async(initialFolder);
                    if (result != null)
                    {
                        return result;
                    }
                    _logger.LogWarning("Win32 API未选择文件，回退到WinUI API");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Win32 API失败，回退到WinUI API");
                }
            }

            // 回退到WinUI API
            return await SelectSingleFileWithWinUIAsync(initialFolder);
        }

        private async Task<AudioFile?> SelectSingleFileWithWin32Async(string initialFolder)
        {
            await Task.CompletedTask; // 保持异步接口一致性
            // ⚠️ 暂时禁用Win32 API以解决崩溃问题
            // TODO: 需要正确实现Win32 API的字符串缓冲区处理
            _logger.LogWarning("Win32 API已暂时禁用，直接使用WinUI API");
            return null; // 强制回退到WinUI API
        }

        private async Task<AudioFile?> SelectSingleFileWithWinUIAsync(string? initialFolder = null)
        {
            try
            {
                var picker = new FileOpenPicker();

                // 获取当前窗口句柄
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(App.MainWindow);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);

                // 设置文件类型过滤器
                foreach (var extension in _supportedExtensions)
                {
                    picker.FileTypeFilter.Add(extension);
                }

                picker.ViewMode = PickerViewMode.List;
                picker.SettingsIdentifier = "BowelSoundLabeler_SingleFile";
                picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;

                // 设置提示文本
                if (!string.IsNullOrEmpty(initialFolder))
                {
                    var folderName = Path.GetFileName(initialFolder);
                    if (string.IsNullOrEmpty(folderName))
                        folderName = initialFolder;
                    picker.CommitButtonText = $"选择 (默认: {folderName})";
                }
                else
                {
                    picker.CommitButtonText = "选择";
                }

                var file = await picker.PickSingleFileAsync();
                if (file != null)
                {
                    var audioFile = AudioFile.FromPath(file.Path);
                    _logger.LogInformation("WinUI API选择了文件: {FilePath}", audioFile.FilePath);
                    return audioFile;
                }

                return null;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WinUI API选择单个文件时发生错误");
                throw;
            }
        }

        public async Task<IList<AudioFile>> SelectMultipleFilesAsync(string? initialFolder = null)
        {
            // 如果提供了初始文件夹，优先使用Win32 API
            if (!string.IsNullOrEmpty(initialFolder) && Directory.Exists(initialFolder))
            {
                try
                {
                    _logger.LogInformation("使用Win32 API选择多个文件，初始文件夹: {Folder}", initialFolder);
                    var result = await SelectMultipleFilesWithWin32Async(initialFolder);
                    if (result.Count > 0)
                    {
                        return result;
                    }
                    _logger.LogWarning("Win32 API未选择文件，回退到WinUI API");
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Win32 API失败，回退到WinUI API");
                }
            }

            // 回退到WinUI API
            return await SelectMultipleFilesWithWinUIAsync(initialFolder);
        }

        private async Task<IList<AudioFile>> SelectMultipleFilesWithWin32Async(string initialFolder)
        {
            await Task.CompletedTask; // 保持异步接口一致性
            // ⚠️ 暂时禁用Win32 API以解决崩溃问题
            // TODO: 需要正确实现Win32 API的字符串缓冲区处理
            _logger.LogWarning("Win32 API已暂时禁用，直接使用WinUI API");
            return new List<AudioFile>(); // 强制回退到WinUI API
        }

        private async Task<IList<AudioFile>> SelectMultipleFilesWithWinUIAsync(string? initialFolder = null)
        {
            try
            {
                var picker = new FileOpenPicker();

                // 获取当前窗口句柄
                var hwnd = WinRT.Interop.WindowNative.GetWindowHandle(App.MainWindow);
                WinRT.Interop.InitializeWithWindow.Initialize(picker, hwnd);

                // 设置文件类型过滤器
                foreach (var extension in _supportedExtensions)
                {
                    picker.FileTypeFilter.Add(extension);
                }

                picker.ViewMode = PickerViewMode.List;
                picker.SettingsIdentifier = "BowelSoundLabeler_MultipleFiles";
                picker.SuggestedStartLocation = PickerLocationId.DocumentsLibrary;

                // 设置提示文本
                if (!string.IsNullOrEmpty(initialFolder))
                {
                    var folderName = Path.GetFileName(initialFolder);
                    if (string.IsNullOrEmpty(folderName))
                        folderName = initialFolder;
                    picker.CommitButtonText = $"选择 (默认: {folderName})";
                }
                else
                {
                    picker.CommitButtonText = "选择";
                }

                var files = await picker.PickMultipleFilesAsync();
                var audioFiles = new List<AudioFile>();

                foreach (var file in files)
                {
                    var audioFile = AudioFile.FromPath(file.Path);
                    audioFiles.Add(audioFile);
                }

                _logger.LogInformation("WinUI API选择了 {Count} 个文件", audioFiles.Count);
                return audioFiles;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "WinUI API选择多个文件时发生错误");
                throw;
            }
        }

        public async Task<bool> ValidateAudioFileAsync(AudioFile audioFile)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (!File.Exists(audioFile.FilePath))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "文件不存在";
                    return false;
                }

                if (!_supportedExtensions.Contains(audioFile.FileExtension, StringComparer.OrdinalIgnoreCase))
                {
                    audioFile.IsValid = false;
                    audioFile.ErrorMessage = "不支持的文件格式";
                    return false;
                }

                audioFile.IsValid = true;
                audioFile.ErrorMessage = null;
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "验证音频文件时发生错误: {FilePath}", audioFile.FilePath);
                audioFile.IsValid = false;
                audioFile.ErrorMessage = ex.Message;
                return false;
            }
        }

        public async Task<bool> RenameFileAsync(string oldPath, string newPath, bool overwrite = false)
        {
            try
            {
                await Task.CompletedTask; // 保持异步接口一致性
                
                if (!File.Exists(oldPath))
                {
                    throw new FileNotFoundException($"源文件不存在: {oldPath}");
                }

                if (File.Exists(newPath) && !overwrite)
                {
                    throw new InvalidOperationException($"目标文件已存在: {newPath}");
                }

                File.Move(oldPath, newPath, overwrite);
                _logger.LogInformation("文件重命名成功: {OldPath} -> {NewPath}", oldPath, newPath);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "重命名文件时发生错误: {OldPath} -> {NewPath}", oldPath, newPath);
                throw;
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            await Task.CompletedTask; // 保持异步接口一致性
            return File.Exists(filePath);
        }

        public IReadOnlyList<string> GetSupportedExtensions()
        {
            return _supportedExtensions;
        }

        private string CreateFileFilter()
        {
            var filter = new StringBuilder();
            filter.Append("支持的文件|");
            filter.Append(string.Join(";", _supportedExtensions.Select(ext => $"*{ext}")));
            filter.Append("|所有文件|*.*");
            filter.Append('\0');
            return filter.ToString();
        }

        private List<string> ParseMultipleFiles(string fileString)
        {
            var files = new List<string>();
            var parts = fileString.Split('\0', StringSplitOptions.RemoveEmptyEntries);

            if (parts.Length == 1)
            {
                // 只选择了一个文件
                files.Add(parts[0]);
            }
            else if (parts.Length > 1)
            {
                // 选择了多个文件，第一个是文件夹路径
                var folder = parts[0];
                for (int i = 1; i < parts.Length; i++)
                {
                    files.Add(Path.Combine(folder, parts[i]));
                }
            }

            return files;
        }
    }
}
