﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System.Diagnostics.CodeAnalysis;


namespace BowelSoundLabeler
{
    public partial class App : global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider
    {
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
        private global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMetaDataProvider __appProvider;

        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        private global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMetaDataProvider _AppProvider
        {
            get
            {
                if (__appProvider == null)
                {
                    __appProvider = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMetaDataProvider();
                }
                return __appProvider;
            }
        }

        /// <summary>
        /// GetXamlType(Type)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(global::System.Type type)
        {
            return _AppProvider.GetXamlType(type);
        }

        /// <summary>
        /// GetXamlType(String)
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(string fullName)
        {
            return _AppProvider.GetXamlType(fullName);
        }

        /// <summary>
        /// GetXmlnsDefinitions()
        /// </summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        public global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[] GetXmlnsDefinitions()
        {
            return _AppProvider.GetXmlnsDefinitions();
        }
    }
}

namespace BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo
{
    /// <summary>
    /// Main class for providing metadata for the app or library
    /// </summary>
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    public sealed partial class XamlMetaDataProvider : global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider
    {
        private global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlTypeInfoProvider _provider = null;

        private global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlTypeInfoProvider Provider
        {
            get
            {
                if (_provider == null)
                {
                    _provider = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlTypeInfoProvider();
                }
                return _provider;
            }
        }

        /// <summary>
        /// GetXamlType(Type)
        /// </summary>
        [global::Windows.Foundation.Metadata.DefaultOverload]
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(global::System.Type type)
        {
            return Provider.GetXamlTypeByType(type);
        }

        /// <summary>
        /// GetXamlType(String)
        /// </summary>
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlType(string fullName)
        {
            return Provider.GetXamlTypeByName(fullName);
        }

        /// <summary>
        /// GetXmlnsDefinitions()
        /// </summary>
        public global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[] GetXmlnsDefinitions()
        {
            return new global::Microsoft.UI.Xaml.Markup.XmlnsDefinition[0];
        }
    }

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlTypeInfoProvider
    {
        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlTypeByType(global::System.Type type)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType;
            lock (_xamlTypeCacheByType) 
            { 
                if (_xamlTypeCacheByType.TryGetValue(type, out xamlType))
                {
                    return xamlType;
                }
                int typeIndex = LookupTypeIndexByType(type);
                if(typeIndex != -1)
                {
                    xamlType = CreateXamlType(typeIndex);
                }
                var userXamlType = xamlType as global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType;
                if(xamlType == null || (userXamlType != null && userXamlType.IsReturnTypeStub && !userXamlType.IsLocalType))
                {
                    global::Microsoft.UI.Xaml.Markup.IXamlType libXamlType = CheckOtherMetadataProvidersForType(type);
                    if (libXamlType != null)
                    {
                        if(libXamlType.IsConstructible || xamlType == null)
                        {
                            xamlType = libXamlType;
                        }
                    }
                }
                if (xamlType != null)
                {
                    _xamlTypeCacheByName.Add(xamlType.FullName, xamlType);
                    _xamlTypeCacheByType.Add(xamlType.UnderlyingType, xamlType);
                }
            }
            return xamlType;
        }

        public global::Microsoft.UI.Xaml.Markup.IXamlType GetXamlTypeByName(string typeName)
        {
            if (string.IsNullOrEmpty(typeName))
            {
                return null;
            }
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType;
            lock (_xamlTypeCacheByType)
            {
                if (_xamlTypeCacheByName.TryGetValue(typeName, out xamlType))
                {
                    return xamlType;
                }
                int typeIndex = LookupTypeIndexByName(typeName);
                if(typeIndex != -1)
                {
                    xamlType = CreateXamlType(typeIndex);
                }
                var userXamlType = xamlType as global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType;
                if(xamlType == null || (userXamlType != null && userXamlType.IsReturnTypeStub && !userXamlType.IsLocalType))
                {
                    global::Microsoft.UI.Xaml.Markup.IXamlType libXamlType = CheckOtherMetadataProvidersForName(typeName);
                    if (libXamlType != null)
                    {
                        if(libXamlType.IsConstructible || xamlType == null)
                        {
                            xamlType = libXamlType;
                        }
                    }
                }
                if (xamlType != null)
                {
                    _xamlTypeCacheByName.Add(xamlType.FullName, xamlType);
                    _xamlTypeCacheByType.Add(xamlType.UnderlyingType, xamlType);
                }
            }
            return xamlType;
        }

        public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMemberByLongName(string longMemberName)
        {
            if (string.IsNullOrEmpty(longMemberName))
            {
                return null;
            }
            global::Microsoft.UI.Xaml.Markup.IXamlMember xamlMember;
            lock (_xamlMembers)
            {
                if (_xamlMembers.TryGetValue(longMemberName, out xamlMember))
                {
                    return xamlMember;
                }
                xamlMember = CreateXamlMember(longMemberName);
                if (xamlMember != null)
                {
                    _xamlMembers.Add(longMemberName, xamlMember);
                }
            }
            return xamlMember;
        }

        global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlType>
                _xamlTypeCacheByName = new global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlType>();

        global::System.Collections.Generic.Dictionary<global::System.Type, global::Microsoft.UI.Xaml.Markup.IXamlType>
                _xamlTypeCacheByType = new global::System.Collections.Generic.Dictionary<global::System.Type, global::Microsoft.UI.Xaml.Markup.IXamlType>();

        global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlMember>
                _xamlMembers = new global::System.Collections.Generic.Dictionary<string, global::Microsoft.UI.Xaml.Markup.IXamlMember>();

        string[] _typeNameTable = null;
        global::System.Type[] _typeTable = null;
        
        private void InitTypeTables()
        {
            _typeNameTable = new string[20];
            _typeNameTable[0] = "Microsoft.UI.Xaml.Controls.XamlControlsResources";
            _typeNameTable[1] = "Microsoft.UI.Xaml.ResourceDictionary";
            _typeNameTable[2] = "Object";
            _typeNameTable[3] = "Boolean";
            _typeNameTable[4] = "Microsoft.UI.Xaml.Media.MicaBackdrop";
            _typeNameTable[5] = "Microsoft.UI.Xaml.Media.SystemBackdrop";
            _typeNameTable[6] = "Microsoft.UI.Composition.SystemBackdrops.MicaKind";
            _typeNameTable[7] = "System.Enum";
            _typeNameTable[8] = "System.ValueType";
            _typeNameTable[9] = "Microsoft.UI.Xaml.Controls.ProgressBar";
            _typeNameTable[10] = "Microsoft.UI.Xaml.Controls.Primitives.RangeBase";
            _typeNameTable[11] = "Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings";
            _typeNameTable[12] = "Microsoft.UI.Xaml.DependencyObject";
            _typeNameTable[13] = "BowelSoundLabeler.MainWindow";
            _typeNameTable[14] = "Microsoft.UI.Xaml.Window";
            _typeNameTable[15] = "BowelSoundLabeler.ViewModels.MainWindowViewModel";
            _typeNameTable[16] = "CommunityToolkit.Mvvm.ComponentModel.ObservableObject";
            _typeNameTable[17] = "Microsoft.UI.Xaml.Controls.TreeViewNode";
            _typeNameTable[18] = "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>";
            _typeNameTable[19] = "Int32";

            _typeTable = new global::System.Type[20];
            _typeTable[0] = typeof(global::Microsoft.UI.Xaml.Controls.XamlControlsResources);
            _typeTable[1] = typeof(global::Microsoft.UI.Xaml.ResourceDictionary);
            _typeTable[2] = typeof(global::System.Object);
            _typeTable[3] = typeof(global::System.Boolean);
            _typeTable[4] = typeof(global::Microsoft.UI.Xaml.Media.MicaBackdrop);
            _typeTable[5] = typeof(global::Microsoft.UI.Xaml.Media.SystemBackdrop);
            _typeTable[6] = typeof(global::Microsoft.UI.Composition.SystemBackdrops.MicaKind);
            _typeTable[7] = typeof(global::System.Enum);
            _typeTable[8] = typeof(global::System.ValueType);
            _typeTable[9] = typeof(global::Microsoft.UI.Xaml.Controls.ProgressBar);
            _typeTable[10] = typeof(global::Microsoft.UI.Xaml.Controls.Primitives.RangeBase);
            _typeTable[11] = typeof(global::Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings);
            _typeTable[12] = typeof(global::Microsoft.UI.Xaml.DependencyObject);
            _typeTable[13] = typeof(global::BowelSoundLabeler.MainWindow);
            _typeTable[14] = typeof(global::Microsoft.UI.Xaml.Window);
            _typeTable[15] = typeof(global::BowelSoundLabeler.ViewModels.MainWindowViewModel);
            _typeTable[16] = typeof(global::CommunityToolkit.Mvvm.ComponentModel.ObservableObject);
            _typeTable[17] = typeof(global::Microsoft.UI.Xaml.Controls.TreeViewNode);
            _typeTable[18] = typeof(global::System.Collections.Generic.IList<global::Microsoft.UI.Xaml.Controls.TreeViewNode>);
            _typeTable[19] = typeof(global::System.Int32);
        }

        private int LookupTypeIndexByName(string typeName)
        {
            if (_typeNameTable == null)
            {
                InitTypeTables();
            }
            for (int i=0; i<_typeNameTable.Length; i++)
            {
                if(0 == string.CompareOrdinal(_typeNameTable[i], typeName))
                {
                    return i;
                }
            }
            return -1;
        }

        private int LookupTypeIndexByType(global::System.Type type)
        {
            if (_typeTable == null)
            {
                InitTypeTables();
            }
            for(int i=0; i<_typeTable.Length; i++)
            {
                if(type == _typeTable[i])
                {
                    return i;
                }
            }
            return -1;
        }

        private object Activate_0_XamlControlsResources() { return new global::Microsoft.UI.Xaml.Controls.XamlControlsResources(); }
        private object Activate_4_MicaBackdrop() { return new global::Microsoft.UI.Xaml.Media.MicaBackdrop(); }
        private object Activate_9_ProgressBar() { return new global::Microsoft.UI.Xaml.Controls.ProgressBar(); }
        private object Activate_13_MainWindow() { return new global::BowelSoundLabeler.MainWindow(); }
        private object Activate_17_TreeViewNode() { return new global::Microsoft.UI.Xaml.Controls.TreeViewNode(); }
        private void StaticInitializer_0_XamlControlsResources() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.XamlControlsResources).TypeHandle);
        private void StaticInitializer_4_MicaBackdrop() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Media.MicaBackdrop).TypeHandle);
        private void StaticInitializer_6_MicaKind() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Composition.SystemBackdrops.MicaKind).TypeHandle);
        private void StaticInitializer_7_Enum() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Enum).TypeHandle);
        private void StaticInitializer_8_ValueType() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.ValueType).TypeHandle);
        private void StaticInitializer_9_ProgressBar() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ProgressBar).TypeHandle);
        private void StaticInitializer_11_ProgressBarTemplateSettings() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings).TypeHandle);
        private void StaticInitializer_13_MainWindow() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::BowelSoundLabeler.MainWindow).TypeHandle);
        private void StaticInitializer_15_MainWindowViewModel() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::BowelSoundLabeler.ViewModels.MainWindowViewModel).TypeHandle);
        private void StaticInitializer_16_ObservableObject() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::CommunityToolkit.Mvvm.ComponentModel.ObservableObject).TypeHandle);
        private void StaticInitializer_17_TreeViewNode() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::Microsoft.UI.Xaml.Controls.TreeViewNode).TypeHandle);
        private void StaticInitializer_18_IList() => global::System.Runtime.CompilerServices.RuntimeHelpers.RunClassConstructor(typeof(global::System.Collections.Generic.IList<global::Microsoft.UI.Xaml.Controls.TreeViewNode>).TypeHandle);
        private void MapAdd_0_XamlControlsResources(object instance, object key, object item)
        {
            var collection = (global::System.Collections.Generic.IDictionary<global::System.Object, global::System.Object>)instance;
            var newKey = (global::System.Object)key;
            var newItem = (global::System.Object)item;
            collection.Add(newKey, newItem);
        }
        private void VectorAdd_18_IList(object instance, object item)
        {
            var collection = (global::System.Collections.Generic.ICollection<global::Microsoft.UI.Xaml.Controls.TreeViewNode>)instance;
            var newItem = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)item;
            collection.Add(newItem);
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CreateXamlType(int typeIndex)
        {
            global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType xamlType = null;
            global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType userType;
            string typeName = _typeNameTable[typeIndex];
            global::System.Type type = _typeTable[typeIndex];

            switch (typeIndex)
            {

            case 0:   //  Microsoft.UI.Xaml.Controls.XamlControlsResources
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.ResourceDictionary"));
                userType.Activator = Activate_0_XamlControlsResources;
                userType.StaticInitializer = StaticInitializer_0_XamlControlsResources;
                userType.DictionaryAdd = MapAdd_0_XamlControlsResources;
                userType.AddMemberName("UseCompactResources");
                xamlType = userType;
                break;

            case 1:   //  Microsoft.UI.Xaml.ResourceDictionary
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 2:   //  Object
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 3:   //  Boolean
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 4:   //  Microsoft.UI.Xaml.Media.MicaBackdrop
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Media.SystemBackdrop"));
                userType.Activator = Activate_4_MicaBackdrop;
                userType.StaticInitializer = StaticInitializer_4_MicaBackdrop;
                userType.AddMemberName("Kind");
                xamlType = userType;
                break;

            case 5:   //  Microsoft.UI.Xaml.Media.SystemBackdrop
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 6:   //  Microsoft.UI.Composition.SystemBackdrops.MicaKind
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.Enum"));
                userType.StaticInitializer = StaticInitializer_6_MicaKind;
                userType.AddEnumValue("Base", global::Microsoft.UI.Composition.SystemBackdrops.MicaKind.Base);
                userType.AddEnumValue("BaseAlt", global::Microsoft.UI.Composition.SystemBackdrops.MicaKind.BaseAlt);
                xamlType = userType;
                break;

            case 7:   //  System.Enum
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("System.ValueType"));
                userType.StaticInitializer = StaticInitializer_7_Enum;
                xamlType = userType;
                break;

            case 8:   //  System.ValueType
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_8_ValueType;
                xamlType = userType;
                break;

            case 9:   //  Microsoft.UI.Xaml.Controls.ProgressBar
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Controls.Primitives.RangeBase"));
                userType.Activator = Activate_9_ProgressBar;
                userType.StaticInitializer = StaticInitializer_9_ProgressBar;
                userType.AddMemberName("IsIndeterminate");
                userType.AddMemberName("ShowError");
                userType.AddMemberName("ShowPaused");
                userType.AddMemberName("TemplateSettings");
                xamlType = userType;
                break;

            case 10:   //  Microsoft.UI.Xaml.Controls.Primitives.RangeBase
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 11:   //  Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.StaticInitializer = StaticInitializer_11_ProgressBarTemplateSettings;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 12:   //  Microsoft.UI.Xaml.DependencyObject
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 13:   //  BowelSoundLabeler.MainWindow
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.Window"));
                userType.Activator = Activate_13_MainWindow;
                userType.StaticInitializer = StaticInitializer_13_MainWindow;
                userType.AddMemberName("ViewModel");
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 14:   //  Microsoft.UI.Xaml.Window
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;

            case 15:   //  BowelSoundLabeler.ViewModels.MainWindowViewModel
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("CommunityToolkit.Mvvm.ComponentModel.ObservableObject"));
                userType.StaticInitializer = StaticInitializer_15_MainWindowViewModel;
                userType.SetIsReturnTypeStub();
                userType.SetIsLocalType();
                xamlType = userType;
                break;

            case 16:   //  CommunityToolkit.Mvvm.ComponentModel.ObservableObject
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Object"));
                userType.StaticInitializer = StaticInitializer_16_ObservableObject;
                xamlType = userType;
                break;

            case 17:   //  Microsoft.UI.Xaml.Controls.TreeViewNode
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, GetXamlTypeByName("Microsoft.UI.Xaml.DependencyObject"));
                userType.Activator = Activate_17_TreeViewNode;
                userType.StaticInitializer = StaticInitializer_17_TreeViewNode;
                userType.AddMemberName("Children");
                userType.AddMemberName("Content");
                userType.AddMemberName("Depth");
                userType.AddMemberName("HasChildren");
                userType.AddMemberName("HasUnrealizedChildren");
                userType.AddMemberName("IsExpanded");
                userType.AddMemberName("Parent");
                userType.SetIsBindable();
                xamlType = userType;
                break;

            case 18:   //  System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>
                userType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType(this, typeName, type, null);
                userType.StaticInitializer = StaticInitializer_18_IList;
                userType.CollectionAdd = VectorAdd_18_IList;
                userType.SetIsReturnTypeStub();
                xamlType = userType;
                break;

            case 19:   //  Int32
                xamlType = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType(typeName, type);
                break;
            }
            return xamlType;
        }

        private global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider> _otherProviders;
        private global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider> OtherProviders
        {
            get
            {
                if(_otherProviders == null)
                {
                    var otherProviders = new global::System.Collections.Generic.List<global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider>();
                    global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider provider;
                    provider = new global::Microsoft.UI.Xaml.XamlTypeInfo.XamlControlsXamlMetaDataProvider() as global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider;
                    otherProviders.Add(provider); 
                    _otherProviders = otherProviders;
                }
                return _otherProviders;
            }
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CheckOtherMetadataProvidersForName(string typeName)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType = null;
            global::Microsoft.UI.Xaml.Markup.IXamlType foundXamlType = null;
            foreach(global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider xmp in OtherProviders)
            {
                xamlType = xmp.GetXamlType(typeName);
                if(xamlType != null)
                {
                    if(xamlType.IsConstructible)    // not Constructible means it might be a Return Type Stub
                    {
                        return xamlType;
                    }
                    foundXamlType = xamlType;
                }
            }
            return foundXamlType;
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlType CheckOtherMetadataProvidersForType(global::System.Type type)
        {
            global::Microsoft.UI.Xaml.Markup.IXamlType xamlType = null;
            global::Microsoft.UI.Xaml.Markup.IXamlType foundXamlType = null;
            foreach(global::Microsoft.UI.Xaml.Markup.IXamlMetadataProvider xmp in OtherProviders)
            {
                xamlType = xmp.GetXamlType(type);
                if(xamlType != null)
                {
                    if(xamlType.IsConstructible)    // not Constructible means it might be a Return Type Stub
                    {
                        return xamlType;
                    }
                    foundXamlType = xamlType;
                }
            }
            return foundXamlType;
        }

        private object get_0_XamlControlsResources_UseCompactResources(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.XamlControlsResources)instance;
            return that.UseCompactResources;
        }
        private void set_0_XamlControlsResources_UseCompactResources(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.XamlControlsResources)instance;
            that.UseCompactResources = (global::System.Boolean)Value;
        }
        private object get_1_MicaBackdrop_Kind(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Media.MicaBackdrop)instance;
            return that.Kind;
        }
        private void set_1_MicaBackdrop_Kind(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Media.MicaBackdrop)instance;
            that.Kind = (global::Microsoft.UI.Composition.SystemBackdrops.MicaKind)Value;
        }
        private object get_2_ProgressBar_IsIndeterminate(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.IsIndeterminate;
        }
        private void set_2_ProgressBar_IsIndeterminate(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            that.IsIndeterminate = (global::System.Boolean)Value;
        }
        private object get_3_ProgressBar_ShowError(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.ShowError;
        }
        private void set_3_ProgressBar_ShowError(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            that.ShowError = (global::System.Boolean)Value;
        }
        private object get_4_ProgressBar_ShowPaused(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.ShowPaused;
        }
        private void set_4_ProgressBar_ShowPaused(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            that.ShowPaused = (global::System.Boolean)Value;
        }
        private object get_5_ProgressBar_TemplateSettings(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.ProgressBar)instance;
            return that.TemplateSettings;
        }
        private object get_6_MainWindow_ViewModel(object instance)
        {
            var that = (global::BowelSoundLabeler.MainWindow)instance;
            return that.ViewModel;
        }
        private object get_7_TreeViewNode_Children(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Children;
        }
        private object get_8_TreeViewNode_Content(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Content;
        }
        private void set_8_TreeViewNode_Content(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.Content = (global::System.Object)Value;
        }
        private object get_9_TreeViewNode_Depth(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Depth;
        }
        private object get_10_TreeViewNode_HasChildren(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasChildren;
        }
        private object get_11_TreeViewNode_HasUnrealizedChildren(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.HasUnrealizedChildren;
        }
        private void set_11_TreeViewNode_HasUnrealizedChildren(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.HasUnrealizedChildren = (global::System.Boolean)Value;
        }
        private object get_12_TreeViewNode_IsExpanded(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.IsExpanded;
        }
        private void set_12_TreeViewNode_IsExpanded(object instance, object Value)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            that.IsExpanded = (global::System.Boolean)Value;
        }
        private object get_13_TreeViewNode_Parent(object instance)
        {
            var that = (global::Microsoft.UI.Xaml.Controls.TreeViewNode)instance;
            return that.Parent;
        }

        private global::Microsoft.UI.Xaml.Markup.IXamlMember CreateXamlMember(string longMemberName)
        {
            global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember xamlMember = null;
            global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType userType;

            switch (longMemberName)
            {
            case "Microsoft.UI.Xaml.Controls.XamlControlsResources.UseCompactResources":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.XamlControlsResources");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "UseCompactResources", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_0_XamlControlsResources_UseCompactResources;
                xamlMember.Setter = set_0_XamlControlsResources_UseCompactResources;
                break;
            case "Microsoft.UI.Xaml.Media.MicaBackdrop.Kind":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Media.MicaBackdrop");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "Kind", "Microsoft.UI.Composition.SystemBackdrops.MicaKind");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_1_MicaBackdrop_Kind;
                xamlMember.Setter = set_1_MicaBackdrop_Kind;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.IsIndeterminate":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "IsIndeterminate", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_2_ProgressBar_IsIndeterminate;
                xamlMember.Setter = set_2_ProgressBar_IsIndeterminate;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.ShowError":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "ShowError", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_3_ProgressBar_ShowError;
                xamlMember.Setter = set_3_ProgressBar_ShowError;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.ShowPaused":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "ShowPaused", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_4_ProgressBar_ShowPaused;
                xamlMember.Setter = set_4_ProgressBar_ShowPaused;
                break;
            case "Microsoft.UI.Xaml.Controls.ProgressBar.TemplateSettings":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.ProgressBar");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "TemplateSettings", "Microsoft.UI.Xaml.Controls.ProgressBarTemplateSettings");
                xamlMember.Getter = get_5_ProgressBar_TemplateSettings;
                xamlMember.SetIsReadOnly();
                break;
            case "BowelSoundLabeler.MainWindow.ViewModel":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("BowelSoundLabeler.MainWindow");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "ViewModel", "BowelSoundLabeler.ViewModels.MainWindowViewModel");
                xamlMember.Getter = get_6_MainWindow_ViewModel;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Children":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "Children", "System.Collections.Generic.IList`1<Microsoft.UI.Xaml.Controls.TreeViewNode>");
                xamlMember.Getter = get_7_TreeViewNode_Children;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Content":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "Content", "Object");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_8_TreeViewNode_Content;
                xamlMember.Setter = set_8_TreeViewNode_Content;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Depth":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "Depth", "Int32");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_9_TreeViewNode_Depth;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.HasChildren":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "HasChildren", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_10_TreeViewNode_HasChildren;
                xamlMember.SetIsReadOnly();
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.HasUnrealizedChildren":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "HasUnrealizedChildren", "Boolean");
                xamlMember.Getter = get_11_TreeViewNode_HasUnrealizedChildren;
                xamlMember.Setter = set_11_TreeViewNode_HasUnrealizedChildren;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.IsExpanded":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "IsExpanded", "Boolean");
                xamlMember.SetIsDependencyProperty();
                xamlMember.Getter = get_12_TreeViewNode_IsExpanded;
                xamlMember.Setter = set_12_TreeViewNode_IsExpanded;
                break;
            case "Microsoft.UI.Xaml.Controls.TreeViewNode.Parent":
                userType = (global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlUserType)GetXamlTypeByName("Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember = new global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlMember(this, "Parent", "Microsoft.UI.Xaml.Controls.TreeViewNode");
                xamlMember.Getter = get_13_TreeViewNode_Parent;
                xamlMember.SetIsReadOnly();
                break;
            }
            return xamlMember;
        }
    }

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlSystemBaseType : global::Microsoft.UI.Xaml.Markup.IXamlType
    {
        string _fullName;
        global::System.Type _underlyingType;

        public XamlSystemBaseType(string fullName, 
            global::System.Type underlyingType)
        {
            _fullName = fullName;
            _underlyingType = underlyingType;
        }

        public string FullName { get { return _fullName; } }

        public global::System.Type UnderlyingType
        {
            get
            {
                return _underlyingType;
            }
        }

        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType BaseType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlMember ContentProperty { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMember(string name) { throw new global::System.NotImplementedException(); }
        virtual public bool IsArray { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsCollection { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsConstructible { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsDictionary { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsMarkupExtension { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsBindable { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsReturnTypeStub { get { throw new global::System.NotImplementedException(); } }
        virtual public bool IsLocalType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType ItemType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType KeyType { get { throw new global::System.NotImplementedException(); } }
        virtual public global::Microsoft.UI.Xaml.Markup.IXamlType BoxedType { get { throw new global::System.NotImplementedException(); } }
        virtual public object ActivateInstance() { throw new global::System.NotImplementedException(); }
        virtual public void AddToMap(object instance, object key, object item)  { throw new global::System.NotImplementedException(); }
        virtual public void AddToVector(object instance, object item)  { throw new global::System.NotImplementedException(); }
        virtual public void RunInitializer()   { throw new global::System.NotImplementedException(); }
        virtual public object CreateFromString(string input)   { throw new global::System.NotImplementedException(); }
    }
    
    internal delegate object Activator();
    internal delegate void StaticInitializer();
    internal delegate void AddToCollection(object instance, object item);
    internal delegate void AddToDictionary(object instance, object key, object item);
    internal delegate object CreateFromStringMethod(string args);
    internal delegate object BoxInstanceMethod(object instance);

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlUserType : global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlSystemBaseType
        , global::Microsoft.UI.Xaml.Markup.IXamlType
    {
        global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlTypeInfoProvider _provider;
        global::Microsoft.UI.Xaml.Markup.IXamlType _baseType;
        global::Microsoft.UI.Xaml.Markup.IXamlType _boxedType;
        bool _isArray;
        bool _isMarkupExtension;
        bool _isBindable;
        bool _isReturnTypeStub;
        bool _isLocalType;

        string _contentPropertyName;
        string _itemTypeName;
        string _keyTypeName;
        global::System.Collections.Generic.Dictionary<string, string> _memberNames;
        global::System.Collections.Generic.Dictionary<string, object> _enumValues;

        public XamlUserType(global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlTypeInfoProvider provider, string fullName, 
            global::System.Type fullType, global::Microsoft.UI.Xaml.Markup.IXamlType baseType)
            :base(fullName, fullType)
        {
            _provider = provider;
            _baseType = baseType;
        }

        // --- Interface methods ----

        override public global::Microsoft.UI.Xaml.Markup.IXamlType BaseType { get { return _baseType; } }
        override public bool IsArray { get { return _isArray; } }
        override public bool IsCollection { get { return (CollectionAdd != null); } }
        override public bool IsConstructible { get { return (Activator != null); } }
        override public bool IsDictionary { get { return (DictionaryAdd != null); } }
        override public bool IsMarkupExtension { get { return _isMarkupExtension; } }
        override public bool IsBindable { get { return _isBindable; } }
        override public bool IsReturnTypeStub { get { return _isReturnTypeStub; } }
        override public bool IsLocalType { get { return _isLocalType; } }
        override public global::Microsoft.UI.Xaml.Markup.IXamlType BoxedType { get { return _boxedType; } }

        override public global::Microsoft.UI.Xaml.Markup.IXamlMember ContentProperty
        {
            get { return _provider.GetMemberByLongName(_contentPropertyName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlType ItemType
        {
            get { return _provider.GetXamlTypeByName(_itemTypeName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlType KeyType
        {
            get { return _provider.GetXamlTypeByName(_keyTypeName); }
        }

        override public global::Microsoft.UI.Xaml.Markup.IXamlMember GetMember(string name)
        {
            if (_memberNames == null)
            {
                return null;
            }
            string longName;
            if (_memberNames.TryGetValue(name, out longName))
            {
                return _provider.GetMemberByLongName(longName);
            }
            return null;
        }

        override public object ActivateInstance()
        {
            return Activator(); 
        }

        override public void AddToMap(object instance, object key, object item) 
        {
            DictionaryAdd(instance, key, item);
        }

        override public void AddToVector(object instance, object item)
        {
            CollectionAdd(instance, item);
        }

        override public void RunInitializer() 
        {
            StaticInitializer();
        }

        override public object CreateFromString(string input)
        {
            if (BoxedType != null)
            {
                return BoxInstance(BoxedType.CreateFromString(input));
            }

            if (CreateFromStringMethod != null)
            {
                return this.CreateFromStringMethod(input);
            }
            else if (_enumValues != null)
            {
                long value = 0;

                string[] valueParts = input.Split(',');

                foreach (string valuePart in valueParts) 
                {
                    object partValue;
                    long enumFieldValue = 0;
                    try
                    {
                        if (_enumValues.TryGetValue(valuePart.Trim(), out partValue))
                        {
                            enumFieldValue = global::System.Convert.ToInt64(partValue);
                        }
                        else
                        {
                            try
                            {
                                enumFieldValue = global::System.Convert.ToInt64(valuePart.Trim());
                            }
                            catch( global::System.FormatException )
                            {
                                foreach( string key in _enumValues.Keys )
                                {
                                    if( string.Compare(valuePart.Trim(), key, global::System.StringComparison.OrdinalIgnoreCase) == 0 )
                                    {
                                        if( _enumValues.TryGetValue(key.Trim(), out partValue) )
                                        {
                                            enumFieldValue = global::System.Convert.ToInt64(partValue);
                                            break;
                                        }
                                    }
                                }
                            }
                        }
                        value |= enumFieldValue; 
                    }
                    catch( global::System.FormatException )
                    {
                        throw new global::System.ArgumentException(input, FullName);
                    }
                }

                return global::System.Convert.ChangeType(value, global::System.Enum.GetUnderlyingType(this.UnderlyingType));
            }
            throw new global::System.ArgumentException(input, FullName);
        }

        // --- End of Interface methods

        public Activator Activator { get; set; }
        public StaticInitializer StaticInitializer { get; set; }
        public AddToCollection CollectionAdd { get; set; }
        public AddToDictionary DictionaryAdd { get; set; }
        public CreateFromStringMethod CreateFromStringMethod {get; set; }
        public BoxInstanceMethod BoxInstance {get; set; }

        public void SetContentPropertyName(string contentPropertyName)
        {
            _contentPropertyName = contentPropertyName;
        }

        public void SetIsArray()
        {
            _isArray = true; 
        }

        public void SetIsMarkupExtension()
        {
            _isMarkupExtension = true;
        }

        public void SetIsBindable()
        {
            _isBindable = true;
        }

        public void SetIsReturnTypeStub()
        {
            _isReturnTypeStub = true;
        }

        public void SetIsLocalType()
        {
            _isLocalType = true;
        }

        public void SetItemTypeName(string itemTypeName)
        {
            _itemTypeName = itemTypeName;
        }

        public void SetKeyTypeName(string keyTypeName)
        {
            _keyTypeName = keyTypeName;
        }

        public void SetBoxedType(global::Microsoft.UI.Xaml.Markup.IXamlType boxedType)
        {
            _boxedType = boxedType;
        }

        public object BoxType<T>(object instance) where T: struct
        {
            T unwrapped = (T)instance;
            return new global::System.Nullable<T>(unwrapped);
        }

        public void AddMemberName(string shortName)
        {
            if(_memberNames == null)
            {
                _memberNames =  new global::System.Collections.Generic.Dictionary<string,string>();
            }
            _memberNames.Add(shortName, FullName + "." + shortName);
        }

        public void AddEnumValue(string name, object value)
        {
            if (_enumValues == null)
            {
                _enumValues = new global::System.Collections.Generic.Dictionary<string, object>();
            }
            _enumValues.Add(name, value);
        }
    }

    internal delegate object Getter(object instance);
    internal delegate void Setter(object instance, object value);

    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.UI.Xaml.Markup.Compiler"," 3.0.0.2506")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    internal partial class XamlMember : global::Microsoft.UI.Xaml.Markup.IXamlMember
    {
        global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlTypeInfoProvider _provider;
        string _name;
        bool _isAttachable;
        bool _isDependencyProperty;
        bool _isReadOnly;

        string _typeName;
        string _targetTypeName;

        public XamlMember(global::BowelSoundLabeler.BowelSoundLabeler_XamlTypeInfo.XamlTypeInfoProvider provider, string name, string typeName)
        {
            _name = name;
            _typeName = typeName;
            _provider = provider;
        }

        public string Name { get { return _name; } }

        public global::Microsoft.UI.Xaml.Markup.IXamlType Type
        {
            get { return _provider.GetXamlTypeByName(_typeName); }
        }

        public void SetTargetTypeName(string targetTypeName)
        {
            _targetTypeName = targetTypeName;
        }
        public global::Microsoft.UI.Xaml.Markup.IXamlType TargetType
        {
            get { return _provider.GetXamlTypeByName(_targetTypeName); }
        }

        public void SetIsAttachable() { _isAttachable = true; }
        public bool IsAttachable { get { return _isAttachable; } }

        public void SetIsDependencyProperty() { _isDependencyProperty = true; }
        public bool IsDependencyProperty { get { return _isDependencyProperty; } }

        public void SetIsReadOnly() { _isReadOnly = true; }
        public bool IsReadOnly { get { return _isReadOnly; } }

        public Getter Getter { get; set; }
        public object GetValue(object instance)
        {
            if (Getter != null)
                return Getter(instance);
            else
                throw new global::System.InvalidOperationException("GetValue");
        }

        public Setter Setter { get; set; }
        public void SetValue(object instance, object value)
        {
            if (Setter != null)
                Setter(instance, value);
            else
                throw new global::System.InvalidOperationException("SetValue");
        }
    }
}

